# Generated by Django 3.2.12 on 2025-08-20 03:18

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='<PERSON>',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source_key', models.Char<PERSON>ield(help_text='Unique identifier for the spider', max_length=100, unique=True)),
                ('spider_class', models.CharField(help_text='Python class name of the spider', max_length=100)),
                ('spider_filename', models.Char<PERSON>ield(help_text='Filename for the spider', max_length=100)),
                ('base_url', models.URLField(help_text='Base URL of the website')),
                ('start_url', models.URLField(help_text='Starting URL for scraping')),
                ('company_name', models.Char<PERSON><PERSON>(blank=True, help_text='Company name extracted from the website', max_length=200)),
                ('source_country', models.CharField(default='ch', help_text='Source country code', max_length=10)),
                ('lang_code', models.Char<PERSON>ield(default='de', help_text='Language code', max_length=10)),
                ('upload', models.BooleanField(default=True)),
                ('is_active', models.BooleanField(default=True)),
                ('custom', models.BooleanField(default=True)),
                ('has_error', models.BooleanField(default=False)),
                ('depth', models.IntegerField(default=1)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('spider_file_path', models.CharField(blank=True, max_length=500)),
                ('config_file_path', models.CharField(blank=True, max_length=500)),
            ],
            options={
                'verbose_name': 'Spider',
                'verbose_name_plural': 'Spiders',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='SpiderGenerationLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_url', models.URLField()),
                ('success', models.BooleanField(default=False)),
                ('error_message', models.TextField(blank=True)),
                ('generation_time', models.DurationField(blank=True, null=True)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('spider', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='generation_logs', to='spiders.spider')),
            ],
            options={
                'verbose_name': 'Spider Generation Log',
                'verbose_name_plural': 'Spider Generation Logs',
                'ordering': ['-created_at'],
            },
        ),
    ]
