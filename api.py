from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, HttpUrl
import json
import re
import os
from urllib.parse import urlparse
from bs4 import BeautifulSoup
from playwright.async_api import async_playwright
from test import client
import asyncio

app = FastAPI(title="Spider Generator API for jobDesk", description="API to generate Scrapy spiders for job scraping")


class SpiderRequest(BaseModel):
    start_url: HttpUrl


class SpiderResponse(BaseModel):
    spider_code: str
    json_config: dict
    success: bool
    message: str


def parse_llm_output(raw_output):
    try:
        if isinstance(raw_output, str):
            if raw_output.strip().startswith('{') and '"spider_code"' in raw_output:
                try:
                    data = json.loads(raw_output)
                    spider_code = data.get("spider_code", "")
                    config_json = data.get("json_config", {})
                    if spider_code:
                        spider_code = spider_code.replace('\\n', '\n')
                        spider_code = spider_code.replace('\\t', '    ')
                        spider_code = spider_code.replace('\\"', '"')
                        spider_code = spider_code.replace("\\'", "'")
                    return spider_code, config_json
                except json.JSONDecodeError:
                    pass
            content = raw_output
            python_match = re.search(r'=== SPIDER CODE ===.*?```python\n(.*?)\n```', content, re.DOTALL)
            if not python_match:
                python_match = re.search(r'```python\n(.*?)\n```', content, re.DOTALL)
            if python_match:
                spider_code = python_match.group(1).strip()
            else:
                spider_match = re.search(r'=== SPIDER CODE ===(.*?)=== JSON CONFIGURATION ===', content, re.DOTALL)
                if spider_match:
                    spider_code = spider_match.group(1).strip()
                    spider_code = re.sub(r'```python\n?|```\n?', '', spider_code)
                else:
                    spider_code = None
            json_match = re.search(r'=== JSON CONFIGURATION ===.*?```json\n(.*?)\n```', content, re.DOTALL)
            if not json_match:
                json_match = re.search(r'```json\n(.*?)\n```', content, re.DOTALL)
            if json_match:
                try:
                    config_json = json.loads(json_match.group(1).strip())
                except json.JSONDecodeError:
                    config_json = None
            else:
                json_content_match = re.search(r'=== JSON CONFIGURATION ===(.*?)(?:$|\n===)', content, re.DOTALL)
                if json_content_match:
                    json_content = json_content_match.group(1).strip()
                    json_content = re.sub(r'```json\n?|```\n?', '', json_content)
                    try:
                        config_json = json.loads(json_content)
                    except json.JSONDecodeError:
                        config_json = None
                else:
                    config_json = None
            return spider_code, config_json
        elif isinstance(raw_output, dict):
            spider_code = raw_output.get("spider_code", "")
            config_json = raw_output.get("json_config", {})
            if spider_code:
                spider_code = spider_code.replace('\\n', '\n')
                spider_code = spider_code.replace('\\t', '    ')
                spider_code = spider_code.replace('\\"', '"')
                spider_code = spider_code.replace("\\'", "'")

            return spider_code, config_json

        return None, None

    except Exception as e:
        print(f"Error parsing LLM output: {e}")
        return None, None


async def extract_job_links(start_url: str):
    try:
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            await page.goto(str(start_url))
            parsed_url = urlparse(str(start_url))
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
            await page.wait_for_load_state("networkidle")
            html = await page.content()
            soup = BeautifulSoup(html, "html.parser")
            for tag in soup.find_all(["script", "style", "path", "svg", "meta"]):
                tag.decompose()
            job_links = soup.find_all('a')
            prompt1 = (
                    f"After reading the HTML, extract all job links from the following HTML. "
                    f"If the link is not complete then add the base url {base_url} to the link. "
                    "Write them in a Python list, do not include any introduction:\n" +
                    "\n".join(str(tag) for tag in job_links)
            )
            chat_completion1 = client.chat.completions.create(
                messages=[{"role": "user", "content": prompt1}],
                model="openai/gpt-oss-120b",
            )
            step1 = chat_completion1.choices[0].message.content.strip()
            if step1.startswith("```"):
                step1 = step1.split("\n", 1)[1].rsplit("\n```", 1)[0]
            links = json.loads(step1)
            await page.close()
            await browser.close()
            return links, base_url
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error extracting job links: {str(e)}")


async def generate_spider_code(start_url: str, base_url: str, first_job_url: str):
    try:
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            await page.goto(first_job_url)
            await page.wait_for_load_state("networkidle")
            html2 = await page.content()
            soup2 = BeautifulSoup(html2, "html.parser")
            for tag2 in soup2.find_all([
                "script", "style", "path", "svg", "meta",
                'button', 'input', 'textarea', "fieldset",
                'form', 'head', 'footer', 'iframe'
            ]):
                tag2.decompose()
            prompt2 = (
                    f"Generate a complete Scrapy spider for job scraping following this EXACT structure:\n\n"

                    f"IMPORTS (copy exactly):\n"
                    f"from jobsitescraper.log_manager import CustomLogger\n"
                    f"import scrapy, re\n"
                    f"import traceback, sys\n"
                    f"from jobsitescraper.utils import env\n"
                    f"from scrapy.exceptions import CloseSpider\n"
                    f"from bs4 import BeautifulSoup\n"
                    f"from datetime import datetime, timezone\n\n"

                    f"CLASS NAME: Extract domain from {base_url} and create class named like 'domainSpider' (e.g., '4plcsComSpider')\n\n"

                    f"CLASS VARIABLES (copy exactly):\n"
                    f"name = '{base_url.replace('https://', '').replace('www.', '')}'\n"
                    f"close_down = False\n"
                    f"config = {{}}\n"
                    f"i = 0\n"
                    f"page_num = 1\n"
                    f"total = 0\n"
                    f"count = -1\n"
                    f"isLive = env('PRODUCTION')\n\n"

                    f"METHOD 1 - __init__(self, _config=None, **kwargs):\n"
                    f"super().__init__(**kwargs)\n"
                    f"if self.isLive == 'True':\n"
                    f"    self.config = _config\n"
                    f"else:\n"
                    f"    self.config = self.get_config()\n\n"

                    f"METHOD 2 - get_config(self) returns dict with EXACT keys:\n"
                    f"SourceKey, BaseUrl, StartUrl, SourceCountry, LangCode, Upload, IsActive, Custom, MaxPagesToCrawl, MaxJobsToCrawl, RecentJobs, DeleteAllJobsOnStart\n"
                    f"DeleteAllJobsOnStart should be True always\n"
                    f"MaxPagesToCrawl should be 10 and MaxJobsToCrawl should be 500 always\n"
                    f"SourceCountry should be 'ch' always\n"
                    f"LangCode should be 'de' always\n"
                    f"Set BaseUrl = '{base_url}', StartUrl = '{start_url}' (or main jobs page)\n\n"

                    f"METHOD 3 - start_requests(self):\n"
                    f"Check config, log events with CustomLogger.LogEvent()\n"
                    f"If RecentJobs=True: yield scrapy.Request to StartUrl with callback=self.parse_recent\n"
                    f"Include try-except with CustomLogger.LogEvent() and traceback.format_exc()\n\n"

                    f"METHOD 4 - parse_recent(self, response):\n"
                    f"Decode response: website_text = response.body.decode('utf-8')\n"
                    f"Create soup: BeautifulSoup(website_text.replace('<', ' <'), 'html.parser')\n"
                    f"Extract job links from HTML using soup.find_all()\n"
                    f"For each link: yield scrapy.Request(link, callback=self.parse_job)\n"
                    f"Include try-except with logging\n\n"

                    f"METHOD 5 - parse_job(self, response) - MOST IMPORTANT:\n"
                    f"Start with: if self.close_down: raise CloseSpider('took down by analyzer')\n"
                    f"Extract using XPath/BeautifulSoup:\n"
                    f"- jobTitle (strip whitespace, default empty string)\n"
                    f"- jobLocation (clean \\xa0|\\xa0 characters), if you dont find the JobLocation then it will be Switzerland \n"
                    f"- Parse description: find main content, remove tags ['a','img','svg','script','style', 'footer', 'textarea', 'button', 'path', 'head'], create cleanContent and rawContent\n"
                    f"Create ad dict with ALL these fields:\n"
                    f"JobTitle, JobLocation, CompanyLogoFileURL, CompanyName, SourceURL, SourceCountry, SourceKey, SourceLangCode, CrawlTimestamp, SourceUID, CleanContent, RawContent, PostedDate\n"
                    f"RawContent is the HTML of the job post, it should have html tags always\n"
                    f"Find the CompanyLogoFileURL from html, if you dont find it then create a default company logo url\n"
                    f"Find the CompanyName from html\n"
                    f"PostedDate and CrawlTimestamp should be datetime.now(timezone.utc).astimezone().isoformat() always\n"
                    f"Extract JobContactEmails: re.findall('\\S+@\\S+', cleanContent.strip('\\n'))\n and make it string not list\n"
                    f"Extract JobContactPhone: re.findall(r'[\\+\\(]?[1-9][0-9 \\-\\(\\)]{{8,}}[0-9]', cleanContent.strip('\\n').replace('\\u00a0', ' ')) and make it string not list\n"
                    f"Upload logic: increment count, check MaxJobsToCrawl limit, yield ad\n"
                    f"Include try-except with logging\n\n"

                    f"METHOD 6 - close(self, reason):\n"
                    f"Log 'Crawler Stopped, Total Jobs: {{self.count}}' with try-except\n\n"

                    f"CRITICAL REQUIREMENTS:\n"
                    f"- Use EXACT same structure as template\n"
                    f"- All timestamps: datetime.now(timezone.utc).astimezone().isoformat()\n"
                    f"- BeautifulSoup cleaning: remove ['a','img','svg','script','style'] with tag.decompose()\n"
                    f"- Text cleaning: re.sub('\\s+', ' ', text)\n"
                    f"- Same error handling pattern with CustomLogger.LogEvent()\n add this only(CustomLogger.LogEvent(self.config['SourceKey'], str(e)))\n"
                    f"- Same variable names and method signatures\n\n"
                    f"- Dont write comments in the code\n\n"
                    f"- if pagination is there then add pagination logic carefully after checking the html\n\n"
                    f"- if you detect heavy javascript in website content then only add scrapy playwright code in start_requests method and parse_recent method\n\n"

                    f"OUTPUT FORMAT:\n"
                    f"Generate TWO separate outputs:\n\n"
                    f"1. PYTHON SPIDER CODE (complete spider class)\n"
                    f"2. JSON CONFIGURATION with this exact structure:\n"
                    f"{{\n"
                    f"  \"SourceKey\": \"domain_without_https_and_dots_replaced_with_underscores\",\n"
                    f"  \"BaseUrl\": \"{base_url}\",\n"
                    f"  \"StartUrl\": \"{start_url}\",\n"
                    f"  \"SourceCountry\": \"ch\",\n"
                    f"  \"LangCode\": \"de\",\n"
                    f"  \"CompanyName\": \"extracted_from_html\",\n"
                    f"  \"SpiderClass\": \"generated_spider_class_name\",\n"
                    f"  \"SpiderFileName\": \"source_key_as_filename\",\n"
                    f"  \"Upload\": true,\n"
                    f"  \"IsActive\": true,\n"
                    f"  \"Custom\": true,\n"
                    f"  \"HasError\": false,\n"
                    f"  \"Depth\": 1\n"
                    f"}}\n\n"

                    f"JSON FIELD REQUIREMENTS:\n"
                    f"- SourceKey: Extract from {base_url}, replace dots with underscores (e.g., 'abb.com' becomes 'abb_com')\n"
                    f"- CompanyName: Extract from the HTML content\n"
                    f"- SpiderClass: The exact class name you generate for the spider\n"
                    f"- SpiderFileName: Same as SourceKey (for file naming)\n"
                    f"- SourceCountry: Always 'ch'\n"
                    f"- LangCode: Always 'de'\n\n"

                    f"Present outputs clearly separated with headers:\n"
                    f"=== SPIDER CODE ===\n"
                    f"[spider code here]\n\n"
                    f"=== JSON CONFIGURATION ===\n"
                    f"[json here]\n\n"

                    f"Analyze this HTML to determine correct selectors for job data extraction:\n"
                    + str(soup2)
            )
            chat_completion2 = client.chat.completions.create(
                messages=[{"role": "user", "content": prompt2}],
                model="openai/gpt-oss-120b"
            )
            raw_output = chat_completion2.choices[0].message.content
            await page.close()
            await browser.close()
            spider_code, config_json = parse_llm_output(raw_output)
            if not spider_code or not config_json:
                raise HTTPException(status_code=500,
                                    detail=f"Failed to parse LLM output properly. Raw output: {raw_output[:200]}...")
            return spider_code, config_json
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating spider code: {str(e)}")


@app.post("/generate-spider", response_model=SpiderResponse)
async def generate_spider(request: SpiderRequest):
    try:
        job_links, base_url = await extract_job_links(str(request.start_url))
        if not job_links:
            raise HTTPException(status_code=400, detail="No job links found on the provided URL")
        first_job_url = job_links[0]
        spider_code, config_json = await generate_spider_code(str(request.start_url), base_url, first_job_url)
        return SpiderResponse(
            spider_code=spider_code,
            json_config=config_json,
            success=True,
            message=f"Successfully generated spider for {base_url}. Found {len(job_links)} job links."
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")



@app.post("/generate-and-save-spider")
async def generate_and_save_spider(request: SpiderRequest):
    try:
        response = await generate_spider(request)
        if response.success:
            spiders_dir = "Spiders"
            if not os.path.exists(spiders_dir):
                os.makedirs(spiders_dir)
            spider_name = response.json_config['SpiderFileName']
            spider_folder = os.path.join(spiders_dir, spider_name)
            if not os.path.exists(spider_folder):
                os.makedirs(spider_folder)
            spider_filename = f"{spider_name}.py"
            config_filename = f"{spider_name}_config.json"
            spider_filepath = os.path.join(spider_folder, spider_filename)
            config_filepath = os.path.join(spider_folder, config_filename)
            spider_exists = os.path.exists(spider_filepath)
            config_exists = os.path.exists(config_filepath)
            with open(spider_filepath, 'w', encoding='utf-8') as f:
                f.write(response.spider_code)
            with open(config_filepath, 'w', encoding='utf-8') as f:
                json.dump(response.json_config, f, indent=2)
            if spider_exists or config_exists:
                message = f"Spider updated successfully in folder: {spider_folder}"
                action = "updated"
            else:
                message = f"Spider generated and saved successfully in folder: {spider_folder}"
                action = "created"
            return {
                "success": True,
                "message": message,
                "action": action,
                "spider_folder": spider_folder,
                "files_created": [spider_filepath, config_filepath],
                "spider_code": response.spider_code,
                "json_config": response.json_config
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to generate spider")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Unexpected error: {str(e)}")
