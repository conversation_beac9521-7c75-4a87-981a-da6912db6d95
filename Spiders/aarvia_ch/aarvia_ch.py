from jobsitescraper.log_manager import CustomLogger
import scrapy, re
import traceback, sys
from jobsitescraper.utils import env
from scrapy.exceptions import CloseSpider
from bs4 import BeautifulSoup
from datetime import datetime, timezone

class AarviaChSpider(scrapy.Spider):
    name = 'aarvia.ch'
    close_down = False
    config = {}
    i = 0
    page_num = 1
    total = 0
    count = -1
    isLive = env('PRODUCTION')

    def __init__(self, _config=None, **kwargs):
        super().__init__(**kwargs)
        if self.isLive == 'True':
            self.config = _config
        else:
            self.config = self.get_config()

    def get_config(self):
        return {
            'SourceKey': 'aarvia_ch',
            'BaseUrl': 'https://aarvia.ch',
            'StartUrl': 'https://aarvia.ch/jobs-and-lehrstellen/aktuelle-stellenangebote',
            'SourceCountry': 'ch',
            'LangCode': 'de',
            'Upload': True,
            'IsActive': True,
            'Custom': True,
            'MaxPagesToCrawl': 10,
            'MaxJobsToCrawl': 500,
            'RecentJobs': True,
            'DeleteAllJobsOnStart': True
        }

    def start_requests(self):
        try:
            CustomLogger.LogEvent(self.config['SourceKey'], 'Crawler started')
            if self.config.get('RecentJobs'):
                yield scrapy.Request(url=self.config['StartUrl'], callback=self.parse_recent, dont_filter=True)
        except Exception as e:
            CustomLogger.LogEvent(self.config['SourceKey'], str(e))
            CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())

    def parse_recent(self, response):
        try:
            website_text = response.body.decode('utf-8')
            soup = BeautifulSoup(website_text.replace('<', ' <'), 'html.parser')
            links = soup.find_all('a', class_='link-node')
            for a in links:
                href = a.get('href')
                if href and href.startswith('/jobs-and-lehrstellen/aktuelle-stellenangebote/'):
                    url = self.config['BaseUrl'] + href
                    yield scrapy.Request(url=url, callback=self.parse_job, dont_filter=True)
            next_page = soup.find('a', {'rel': 'next'})
            if next_page and self.page_num < self.config['MaxPagesToCrawl']:
                self.page_num += 1
                next_href = next_page.get('href')
                if next_href:
                    next_url = self.config['BaseUrl'] + next_href
                    yield scrapy.Request(url=next_url, callback=self.parse_recent, dont_filter=True)
        except Exception as e:
            CustomLogger.LogEvent(self.config['SourceKey'], str(e))
            CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())

    def parse_job(self, response):
        try:
            if self.close_down:
                raise CloseSpider('took down by analyzer')
            website_text = response.body.decode('utf-8')
            soup = BeautifulSoup(website_text.replace('<', ' <'), 'html.parser')
            title_tag = soup.find(['h1', 'h2'], class_=re.compile('.*title.*', re.I))
            jobTitle = title_tag.get_text(strip=True) if title_tag else ''
            location_tag = soup.find(text=re.compile('Ort|Location', re.I))
            if location_tag:
                jobLocation = re.sub(r'\xa0', ' ', location_tag.parent.get_text(strip=True))
            else:
                jobLocation = 'Switzerland'
            for tag_name in ['a', 'img', 'svg', 'script', 'style', 'footer', 'textarea', 'button', 'path', 'head']:
                for t in soup.find_all(tag_name):
                    t.decompose()
            raw_html = str(soup)
            clean_text = soup.get_text(strip=True)
            clean_text = re.sub(r'\s+', ' ', clean_text)
            CleanContent = clean_text
            RawContent = raw_html
            logo_tag = soup.find('img', {'alt': re.compile('logo', re.I)})
            if logo_tag and logo_tag.get('src'):
                CompanyLogoFileURL = logo_tag['src'] if logo_tag['src'].startswith('http') else self.config['BaseUrl'] + logo_tag['src']
            else:
                CompanyLogoFileURL = 'https://aarvia.ch/_Resources/Static/Packages/Abte.Site/public/img/logo-weiss.svg'
            company_name_tag = soup.find('meta', {'property': 'og:site_name'})
            if company_name_tag and company_name_tag.get('content'):
                CompanyName = company_name_tag['content']
            else:
                CompanyName = 'Aarvia'
            SourceURL = response.url
            SourceCountry = self.config['SourceCountry']
            SourceKey = self.config['SourceKey']
            SourceLangCode = self.config['LangCode']
            CrawlTimestamp = datetime.now(timezone.utc).astimezone().isoformat()
            SourceUID = response.url
            emails = re.findall(r'\S+@\S+', CleanContent.strip('\n'))
            JobContactEmails = ', '.join(emails)
            phones = re.findall(r'[\+\(]?[1-9][0-9 \-\(\)]{8,}[0-9]', CleanContent.strip('\n').replace('\u00a0', ' '))
            JobContactPhone = ', '.join(phones)
            PostedDate = datetime.now(timezone.utc).astimezone().isoformat()
            ad = {
                'JobTitle': jobTitle,
                'JobLocation': jobLocation,
                'CompanyLogoFileURL': CompanyLogoFileURL,
                'CompanyName': CompanyName,
                'SourceURL': SourceURL,
                'SourceCountry': SourceCountry,
                'SourceKey': SourceKey,
                'SourceLangCode': SourceLangCode,
                'CrawlTimestamp': CrawlTimestamp,
                'SourceUID': SourceUID,
                'CleanContent': CleanContent,
                'RawContent': RawContent,
                'PostedDate': PostedDate,
                'JobContactEmails': JobContactEmails,
                'JobContactPhone': JobContactPhone
            }
            self.count += 1
            if self.config.get('MaxJobsToCrawl') and self.count >= self.config['MaxJobsToCrawl']:
                self.close_down = True
                raise CloseSpider('max jobs limit reached')
            yield ad
        except Exception as e:
            CustomLogger.LogEvent(self.config['SourceKey'], str(e))
            CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())

    def close(self, reason):
        try:
            CustomLogger.LogEvent(self.config['SourceKey'], f'Crawler Stopped, Total Jobs: {self.count}')
        except Exception as e:
            CustomLogger.LogEvent(self.config['SourceKey'], str(e))
            CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())