{% extends 'base.html' %}

{% block title %}Spider Dashboard - Spider Manager{% endblock %}

{% block content %}
<div x-data="spiderDashboard()" x-cloak>
    <!-- Header Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Spider Dashboard</h1>
                <p class="mt-2 text-gray-600">Manage and generate web scraping spiders</p>
            </div>
            <div class="mt-4 sm:mt-0">
                <button @click="showGenerateModal = true" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2">
                    <i class="fas fa-plus"></i>
                    <span>Generate Spider</span>
                </button>
            </div>
        </div>
        
        <!-- Stats -->
        <div class="mt-6 grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div class="bg-blue-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-spider text-blue-600 text-2xl mr-3"></i>
                    <div>
                        <p class="text-sm text-blue-600 font-medium">Total Spiders</p>
                        <p class="text-2xl font-bold text-blue-900">{{ total_spiders }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-green-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-600 text-2xl mr-3"></i>
                    <div>
                        <p class="text-sm text-green-600 font-medium">Active Spiders</p>
                        <p class="text-2xl font-bold text-green-900">{{ page_obj.object_list|length }}</p>
                    </div>
                </div>
            </div>
            <div class="bg-yellow-50 p-4 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-clock text-yellow-600 text-2xl mr-3"></i>
                    <div>
                        <p class="text-sm text-yellow-600 font-medium">Recent Updates</p>
                        <p class="text-2xl font-bold text-yellow-900">{{ page_obj.object_list|slice:":5"|length }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Spider Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {% for spider in page_obj %}
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
            <div class="p-6">
                <!-- Spider Header -->
                <div class="flex items-start justify-between mb-4">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold text-gray-900 truncate">{{ spider.company_name|default:spider.source_key }}</h3>
                        <p class="text-sm text-gray-500 truncate">{{ spider.source_key }}</p>
                    </div>
                    <div class="flex items-center space-x-1">
                        {% if spider.is_active %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-circle text-green-400 mr-1" style="font-size: 6px;"></i>
                                Active
                            </span>
                        {% else %}
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                <i class="fas fa-circle text-gray-400 mr-1" style="font-size: 6px;"></i>
                                Inactive
                            </span>
                        {% endif %}
                    </div>
                </div>

                <!-- Spider Info -->
                <div class="space-y-2 mb-4">
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-globe w-4 mr-2"></i>
                        <span class="truncate">{{ spider.base_url }}</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-flag w-4 mr-2"></i>
                        <span>{{ spider.source_country|upper }} - {{ spider.lang_code|upper }}</span>
                    </div>
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-calendar w-4 mr-2"></i>
                        <span>{{ spider.updated_at|date:"M d, Y" }}</span>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex space-x-2">
                    <a href="{% url 'spiders:detail' spider.source_key %}" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-3 rounded-md text-sm font-medium transition-colors duration-200">
                        <i class="fas fa-eye mr-1"></i>
                        View
                    </a>
                    <button @click="editSpider('{{ spider.source_key }}')" class="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-3 rounded-md text-sm font-medium transition-colors duration-200">
                        <i class="fas fa-edit mr-1"></i>
                        Edit
                    </button>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-span-full">
            <div class="text-center py-12">
                <i class="fas fa-spider text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No spiders found</h3>
                <p class="text-gray-500 mb-6">Get started by generating your first spider</p>
                <button @click="showGenerateModal = true" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i>
                    Generate Your First Spider
                </button>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="mt-8 flex justify-center">
        <nav class="flex items-center space-x-2">
            {% if page_obj.has_previous %}
                <a href="?page={{ page_obj.previous_page_number }}" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                    <i class="fas fa-chevron-left"></i>
                </a>
            {% endif %}
            
            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <span class="px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-300 rounded-md">{{ num }}</span>
                {% else %}
                    <a href="?page={{ num }}" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">{{ num }}</a>
                {% endif %}
            {% endfor %}
            
            {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                    <i class="fas fa-chevron-right"></i>
                </a>
            {% endif %}
        </nav>
    </div>
    {% endif %}

    <!-- Generate Spider Modal -->
    <div x-show="showGenerateModal" x-cloak class="fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div @click.away="showGenerateModal = false" class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Generate New Spider</h3>
                    <button @click="showGenerateModal = false" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form @submit.prevent="generateSpider()">
                    <div class="mb-4">
                        <label for="startUrl" class="block text-sm font-medium text-gray-700 mb-2">Job Listing URL</label>
                        <input 
                            type="url" 
                            id="startUrl" 
                            x-model="startUrl" 
                            required 
                            placeholder="https://example.com/jobs"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                        <p class="mt-1 text-xs text-gray-500">Enter the URL of the job listings page</p>
                    </div>
                    
                    <div class="flex space-x-3">
                        <button 
                            type="button" 
                            @click="showGenerateModal = false" 
                            class="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
                        >
                            Cancel
                        </button>
                        <button 
                            type="submit" 
                            :disabled="generating"
                            class="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <span x-show="!generating">Generate</span>
                            <span x-show="generating" class="flex items-center justify-center">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                Generating...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function spiderDashboard() {
    return {
        showGenerateModal: false,
        generating: false,
        startUrl: '',
        
        async generateSpider() {
            if (!this.startUrl) return;
            
            this.generating = true;
            showLoading();
            
            try {
                const response = await fetch('{% url "spiders:generate" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrftoken
                    },
                    body: JSON.stringify({
                        start_url: this.startUrl
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showToast(`Spider ${data.action} successfully! Found ${data.job_links_found} job links.`, 'success');
                    this.showGenerateModal = false;
                    this.startUrl = '';
                    // Reload page to show new spider
                    setTimeout(() => window.location.reload(), 1500);
                } else {
                    showToast(`Error: ${data.error}`, 'error');
                }
            } catch (error) {
                showToast(`Error: ${error.message}`, 'error');
            } finally {
                this.generating = false;
                hideLoading();
            }
        },
        
        editSpider(sourceKey) {
            window.location.href = `/spiders/${sourceKey}/`;
        }
    }
}
</script>
{% endblock %}
