import json
from urllib.parse import urlparse
from bs4 import Beautiful<PERSON><PERSON><PERSON>
from playwright.sync_api import sync_playwright
from test import client

with sync_playwright() as p:
    browser = p.chromium.launch(headless=True)
    page = browser.new_page()
    starturl = "https://aarvia.ch/jobs-and-lehrstellen/aktuelle-stellenangebote"
    page.goto(starturl)
    parsed_url = urlparse(starturl)
    base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
    page.wait_for_load_state("networkidle")
    html = page.content()
    soup = BeautifulSoup(html, "html.parser")
    for tag in soup.find_all(["script", "style", "path", "svg", "meta"]):
        tag.decompose()
    job_links = soup.find_all('a')

    prompt1 = (
            f"After reading the HTML, extract all job links from the following HTML. "
            f"If the link is not complete then add the base url {base_url} to the link. "
            "Write them in a Python list, do not include any introduction:\n" +
            "\n".join(str(tag) for tag in job_links)
    )

    chat_completion1 = client.chat.completions.create(
        messages=[{"role": "user", "content": prompt1}],
        model="openai/gpt-oss-120b",
    )

    step1 = chat_completion1.choices[0].message.content.strip()
    if step1.startswith("```"):
        step1 = step1.split("\n", 1)[1].rsplit("\n```", 1)[0]
    links = json.loads(step1)
    first_job_url = links[0]

    page.close()
    page = browser.new_page()
    page.goto(first_job_url)
    page.wait_for_load_state("networkidle")
    html2 = page.content()
    soup2 = BeautifulSoup(html2, "html.parser")
    for tag2 in soup2.find_all([
        "script", "style", "path", "svg", "meta",
        'button', 'input', 'textarea', "fieldset",
        'form', 'head', 'footer', 'iframe'
    ]):
        tag2.decompose()

    prompt2 = (
            f"Generate a complete Scrapy spider for job scraping following this EXACT structure:\n\n"

            f"IMPORTS (copy exactly):\n"
            f"from jobsitescraper.log_manager import CustomLogger\n"
            f"import scrapy, re\n"
            f"import traceback, sys\n"
            f"from jobsitescraper.utils import env\n"
            f"from scrapy.exceptions import CloseSpider\n"
            f"from bs4 import BeautifulSoup\n"
            f"from datetime import datetime, timezone\n\n"

            f"CLASS NAME: Extract domain from {base_url} and create class named like 'domainSpider' (e.g., '4plcsComSpider')\n\n"

            f"CLASS VARIABLES (copy exactly):\n"
            f"name = '{base_url.replace('https://', '').replace('www.', '')}'\n"
            f"close_down = False\n"
            f"config = {{}}\n"
            f"i = 0\n"
            f"page_num = 1\n"
            f"total = 0\n"
            f"count = -1\n"
            f"isLive = env('PRODUCTION')\n\n"

            f"METHOD 1 - __init__(self, _config=None, **kwargs):\n"
            f"super().__init__(**kwargs)\n"
            f"if self.isLive == 'True':\n"
            f"    self.config = _config\n"
            f"else:\n"
            f"    self.config = self.get_config()\n\n"

            f"METHOD 2 - get_config(self) returns dict with EXACT keys:\n"
            f"SourceKey, BaseUrl, StartUrl, SourceCountry, LangCode, Upload, IsActive, Custom, MaxPagesToCrawl, MaxJobsToCrawl, RecentJobs, DeleteAllJobsOnStart\n"
            f"DeleteAllJobsOnStart should be True always\n"
            f"MaxPagesToCrawl should be 10 and MaxJobsToCrawl should be 500 always\n"
            f"SourceCountry should be 'ch' always\n"
            f"LangCode should be 'de' always\n"
            f"Set BaseUrl = '{base_url}', StartUrl = '{starturl}' (or main jobs page)\n\n"

            f"METHOD 3 - start_requests(self):\n"
            f"Check config, log events with CustomLogger.LogEvent()\n"
            f"If RecentJobs=True: yield scrapy.Request to StartUrl with callback=self.parse_recent\n"
            f"Include try-except with CustomLogger.LogEvent() and traceback.format_exc()\n\n"

            f"METHOD 4 - parse_recent(self, response):\n"
            f"Decode response: website_text = response.body.decode('utf-8')\n"
            f"Create soup: BeautifulSoup(website_text.replace('<', ' <'), 'html.parser')\n"
            f"Extract job links from HTML using soup.find_all()\n"
            f"For each link: yield scrapy.Request(link, callback=self.parse_job)\n"
            f"Include try-except with logging\n\n"

            f"METHOD 5 - parse_job(self, response) - MOST IMPORTANT:\n"
            f"Start with: if self.close_down: raise CloseSpider('took down by analyzer')\n"
            f"Extract using XPath/BeautifulSoup:\n"
            f"- jobTitle (strip whitespace, default empty string)\n"
            f"- jobLocation (clean \\xa0|\\xa0 characters), if you dont find the JobLocation then it will be Switzerland \n"
            f"- Parse description: find main content, remove tags ['a','img','svg','script','style', 'footer', 'textarea', 'button', 'path', 'head'], create cleanContent and rawContent\n"
            f"Create ad dict with ALL these fields:\n"
            f"JobTitle, JobLocation, CompanyLogoFileURL, CompanyName, SourceURL, SourceCountry, SourceKey, SourceLangCode, CrawlTimestamp, SourceUID, CleanContent, RawContent, PostedDate\n"
            f"RawContent is the HTML of the job post, it should have html tags always\n"
            f"Find the CompanyLogoFileURL from html, if you dont find it then create a default company logo url\n"
            f"Find the CompanyName from html\n"
            f"PostedDate and CrawlTimestamp should be datetime.now(timezone.utc).astimezone().isoformat() always\n"
            f"Extract JobContactEmails: re.findall('\\S+@\\S+', cleanContent.strip('\\n'))\n and make it string not list\n"
            f"Extract JobContactPhone: re.findall(r'[\\+\\(]?[1-9][0-9 \\-\\(\\)]{{8,}}[0-9]', cleanContent.strip('\\n').replace('\\u00a0', ' ')) and make it string not list\n"
            f"Upload logic: increment count, check MaxJobsToCrawl limit, yield ad\n"
            f"Include try-except with logging\n\n"

            f"METHOD 6 - close(self, reason):\n"
            f"Log 'Crawler Stopped, Total Jobs: {{self.count}}' with try-except\n\n"

            f"CRITICAL REQUIREMENTS:\n"
            f"- Use EXACT same structure as template\n"
            f"- All timestamps: datetime.now(timezone.utc).astimezone().isoformat()\n"
            f"- BeautifulSoup cleaning: remove ['a','img','svg','script','style'] with tag.decompose()\n"
            f"- Text cleaning: re.sub('\\s+', ' ', text)\n"
            f"- Same error handling pattern with CustomLogger.LogEvent()\n add this only(CustomLogger.LogEvent(self.config['SourceKey'], str(e)))\n"
            f"- Same variable names and method signatures\n\n"
            f"- Dont write comments in the code\n\n"
            f"- if pagination is there then add pagination logic carefully after checking the html\n\n"
            f"- if you detect heavy javascript in website content then only add scrapy playwright code in start_requests method and parse_recent method\n\n"

            f"OUTPUT FORMAT:\n"
            f"Generate TWO separate outputs:\n\n"
            f"1. PYTHON SPIDER CODE (complete spider class)\n"
            f"2. JSON CONFIGURATION with this exact structure:\n"
            f"{{\n"
            f"  \"SourceKey\": \"domain_without_https_and_www\",\n"
            f"  \"BaseUrl\": \"{base_url}\",\n"
            f"  \"StartUrl\": \"{starturl}\",\n"
            f"  \"SourceCountry\": \"ch\",\n"
            f"  \"LangCode\": \"de\",\n"
            f"  \"CompanyName\": \"extracted_from_html\",\n"
            f"  \"SpiderClass\": \"generated_spider_class_name\",\n"
            f"  \"SpiderFileName\": \"source_key_as_filename\"\n"
            f"}}\n\n"

            f"JSON FIELD REQUIREMENTS:\n"
            f"- SourceKey: Extract domain from {base_url}, remove https:// and www. but keep dots (e.g., 'https://aarvia.com/jobs/' becomes 'aarvia.com')\n"
            f"- CompanyName: Extract from the HTML content\n"
            f"- SpiderClass: The exact class name you generate for the spider\n"
            f"- SpiderFileName: Same as SourceKey (for file naming)\n"
            f"- SourceCountry: Always 'ch'\n"
            f"- LangCode: Always 'de'\n\n"
            f"- Upload: True\n"
            f"- IsActive: True\n"
            f"- Custom: True\n"
            f"- HasError: False\n"
            f"- Depth: 1\n"

            f"Present outputs clearly separated with headers:\n"
            f"=== SPIDER CODE ===\n"
            f"[spider code here]\n\n"
            f"=== JSON CONFIGURATION ===\n"
            f"[json here]\n\n"

            f"Analyze this HTML to determine correct selectors for job data extraction:\n"
            + str(soup2)
    )

    chat_completion2 = client.chat.completions.create(
        messages=[{"role": "user", "content": prompt2}],
        model="openai/gpt-oss-120b")
    spider_code = chat_completion2.choices[0].message.content
    print(spider_code)
