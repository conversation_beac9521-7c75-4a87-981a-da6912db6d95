import json
import os
import asyncio
from datetime import datetime, timedelta
from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from django.conf import settings
from django.core.paginator import Paginator
from .models import Spider, SpiderGenerationLog
from .services import SpiderGenerationService
from urllib.parse import urlparse


def index(request):
    """Main dashboard view showing all spiders"""
    # Load spiders from filesystem that might not be in database
    filesystem_spiders = load_spiders_from_filesystem()
    filesystem_source_keys = {fs_spider['source_key'] for fs_spider in filesystem_spiders}

    # Remove spiders from database that no longer exist in filesystem
    Spider.objects.exclude(source_key__in=filesystem_source_keys).delete()

    # Sync filesystem spiders with database
    for fs_spider in filesystem_spiders:
        spider, created = Spider.objects.get_or_create(
            source_key=fs_spider['source_key'],
            defaults=fs_spider
        )
        if not created:
            # Update existing spider with filesystem data
            for key, value in fs_spider.items():
                setattr(spider, key, value)
            spider.save()

    # Get spiders that exist in filesystem
    spiders = Spider.objects.filter(source_key__in=filesystem_source_keys)

    # Handle search
    search_query = request.GET.get('search', '').strip()
    if search_query:
        spiders = spiders.filter(source_key__icontains=search_query)

    # Pagination
    paginator = Paginator(spiders, 12)  # Show 12 spiders per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'total_spiders': Spider.objects.filter(source_key__in=filesystem_source_keys).count(),
        'search_query': search_query,
        'filtered_count': spiders.count(),
    }
    return render(request, 'spiders/index.html', context)


def load_spiders_from_filesystem():
    """Load spider configurations from the filesystem"""
    spiders = []
    spiders_dir = settings.SPIDERS_DIR

    if not os.path.exists(spiders_dir):
        return spiders

    for spider_folder in os.listdir(spiders_dir):
        spider_path = os.path.join(spiders_dir, spider_folder)
        if os.path.isdir(spider_path):
            config_file = os.path.join(spider_path, f"{spider_folder}_config.json")
            spider_file = os.path.join(spider_path, f"{spider_folder}.py")

            # Only include spiders that have both config file and spider file
            if os.path.exists(config_file) and os.path.exists(spider_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)

                    spider_data = {
                        'source_key': config.get('SourceKey', spider_folder),
                        'spider_class': config.get('SpiderClass', ''),
                        'spider_filename': config.get('SpiderFileName', spider_folder),
                        'base_url': config.get('BaseUrl', ''),
                        'start_url': config.get('StartUrl', ''),
                        'company_name': config.get('CompanyName', ''),
                        'source_country': config.get('SourceCountry', 'ch'),
                        'lang_code': config.get('LangCode', 'de'),
                        'upload': config.get('Upload', True),
                        'is_active': config.get('IsActive', True),
                        'custom': config.get('Custom', True),
                        'has_error': config.get('HasError', False),
                        'depth': config.get('Depth', 1),
                        'spider_file_path': os.path.relpath(spider_file, settings.SPIDERS_DIR),
                        'config_file_path': os.path.relpath(config_file, settings.SPIDERS_DIR),
                    }
                    spiders.append(spider_data)
                except (json.JSONDecodeError, Exception) as e:
                    print(f"Error loading spider config {config_file}: {e}")

    return spiders


def spider_detail(request, source_key):
    """View spider details, code, and configuration"""
    spider = get_object_or_404(Spider, source_key=source_key)

    # Check if spider files still exist in filesystem
    spider_file_full_path = os.path.join(settings.SPIDERS_DIR, spider.spider_file_path) if spider.spider_file_path else None
    config_file_full_path = os.path.join(settings.SPIDERS_DIR, spider.config_file_path) if spider.config_file_path else None

    # If files don't exist, redirect to index with error message
    if (spider_file_full_path and not os.path.exists(spider_file_full_path)) or \
       (config_file_full_path and not os.path.exists(config_file_full_path)):
        messages.error(request, f"Spider files for '{source_key}' have been deleted from the filesystem.")
        return redirect('spiders:index')

    # Load spider code and config from filesystem
    spider_code = ""
    config_json = {}

    if spider_file_full_path and os.path.exists(spider_file_full_path):
        try:
            with open(spider_file_full_path, 'r', encoding='utf-8') as f:
                spider_code = f.read()
        except Exception as e:
            messages.error(request, f"Error reading spider file: {e}")

    if config_file_full_path and os.path.exists(config_file_full_path):
        try:
            with open(config_file_full_path, 'r', encoding='utf-8') as f:
                config_json = json.load(f)
        except Exception as e:
            messages.error(request, f"Error reading config file: {e}")

    # Get recent generation logs
    recent_logs = spider.generation_logs.all()[:5]

    context = {
        'spider': spider,
        'spider_code': spider_code,
        'config_json': json.dumps(config_json, indent=2),
        'recent_logs': recent_logs,
    }
    return render(request, 'spiders/detail.html', context)


@csrf_exempt
@require_http_methods(["POST"])
def generate_spider(request):
    """Generate a new spider from URL"""
    try:
        data = json.loads(request.body)
        start_url = data.get('start_url')

        if not start_url:
            return JsonResponse({'success': False, 'error': 'Start URL is required'})

        # Run the async spider generation
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            start_time = datetime.now()

            # Extract job links
            job_links, base_url = loop.run_until_complete(
                SpiderGenerationService.extract_job_links(start_url)
            )

            if not job_links:
                return JsonResponse({'success': False, 'error': 'No job links found on the provided URL'})

            # Generate spider code
            first_job_url = job_links[0]
            spider_code, config_json = loop.run_until_complete(
                SpiderGenerationService.generate_spider_code(start_url, base_url, first_job_url)
            )

            # Save files
            file_info = SpiderGenerationService.save_spider_files(spider_code, config_json)

            # Create or update spider in database
            spider_data = {
                'spider_class': config_json.get('SpiderClass', ''),
                'spider_filename': config_json.get('SpiderFileName', ''),
                'base_url': config_json.get('BaseUrl', ''),
                'start_url': config_json.get('StartUrl', ''),
                'company_name': config_json.get('CompanyName', ''),
                'source_country': config_json.get('SourceCountry', 'ch'),
                'lang_code': config_json.get('LangCode', 'de'),
                'upload': config_json.get('Upload', True),
                'is_active': config_json.get('IsActive', True),
                'custom': config_json.get('Custom', True),
                'has_error': config_json.get('HasError', False),
                'depth': config_json.get('Depth', 1),
                'spider_file_path': os.path.relpath(file_info['spider_filepath'], settings.SPIDERS_DIR),
                'config_file_path': os.path.relpath(file_info['config_filepath'], settings.SPIDERS_DIR),
            }

            spider, created = Spider.objects.update_or_create(
                source_key=config_json['SourceKey'],
                defaults=spider_data
            )

            # Log the generation
            generation_time = datetime.now() - start_time
            SpiderGenerationLog.objects.create(
                spider=spider,
                start_url=start_url,
                success=True,
                generation_time=generation_time
            )

            return JsonResponse({
                'success': True,
                'message': f"Spider {'updated' if not created else 'created'} successfully",
                'spider_id': spider.id,
                'source_key': spider.source_key,
                'action': file_info['action'],
                'job_links_found': len(job_links)
            })

        finally:
            loop.close()

    except Exception as e:
        # Log the failed generation
        try:
            data = json.loads(request.body)
            start_url = data.get('start_url', '')
            if start_url:
                # Try to create a basic spider record for logging
                parsed_url = urlparse(start_url)
                source_key = parsed_url.netloc.replace('www.', '')
                spider, _ = Spider.objects.get_or_create(
                    source_key=source_key,
                    defaults={'base_url': f"{parsed_url.scheme}://{parsed_url.netloc}", 'start_url': start_url}
                )
                SpiderGenerationLog.objects.create(
                    spider=spider,
                    start_url=start_url,
                    success=False,
                    error_message=str(e)
                )
        except:
            pass

        return JsonResponse({'success': False, 'error': str(e)})


@csrf_exempt
@require_http_methods(["POST"])
def save_spider_code(request, source_key):
    """Save edited spider code"""
    try:
        spider = get_object_or_404(Spider, source_key=source_key)
        data = json.loads(request.body)
        spider_code = data.get('spider_code', '')

        if not spider_code:
            return JsonResponse({'success': False, 'error': 'Spider code is required'})

        # Save to filesystem
        if spider.spider_file_path:
            spider_file_full_path = os.path.join(settings.SPIDERS_DIR, spider.spider_file_path)
            try:
                with open(spider_file_full_path, 'w', encoding='utf-8') as f:
                    f.write(spider_code)

                spider.updated_at = datetime.now()
                spider.save()

                return JsonResponse({'success': True, 'message': 'Spider code saved successfully'})
            except Exception as e:
                return JsonResponse({'success': False, 'error': f'Error saving spider code: {str(e)}'})
        else:
            return JsonResponse({'success': False, 'error': 'Spider file path not found'})

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@csrf_exempt
@require_http_methods(["POST"])
def save_spider_config(request, source_key):
    """Save edited spider configuration"""
    try:
        spider = get_object_or_404(Spider, source_key=source_key)
        data = json.loads(request.body)
        config_json = data.get('config_json', {})

        if not config_json:
            return JsonResponse({'success': False, 'error': 'Configuration is required'})

        # Validate JSON
        try:
            if isinstance(config_json, str):
                config_json = json.loads(config_json)
        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'Invalid JSON format'})

        # Save to filesystem
        if spider.config_file_path:
            config_file_full_path = os.path.join(settings.SPIDERS_DIR, spider.config_file_path)
            try:
                with open(config_file_full_path, 'w', encoding='utf-8') as f:
                    json.dump(config_json, f, indent=2)

                # Update spider model with new config values
                spider.spider_class = config_json.get('SpiderClass', spider.spider_class)
                spider.spider_filename = config_json.get('SpiderFileName', spider.spider_filename)
                spider.base_url = config_json.get('BaseUrl', spider.base_url)
                spider.start_url = config_json.get('StartUrl', spider.start_url)
                spider.company_name = config_json.get('CompanyName', spider.company_name)
                spider.source_country = config_json.get('SourceCountry', spider.source_country)
                spider.lang_code = config_json.get('LangCode', spider.lang_code)
                spider.upload = config_json.get('Upload', spider.upload)
                spider.is_active = config_json.get('IsActive', spider.is_active)
                spider.custom = config_json.get('Custom', spider.custom)
                spider.has_error = config_json.get('HasError', spider.has_error)
                spider.depth = config_json.get('Depth', spider.depth)
                spider.updated_at = datetime.now()
                spider.save()

                return JsonResponse({'success': True, 'message': 'Configuration saved successfully'})
            except Exception as e:
                return JsonResponse({'success': False, 'error': f'Error saving configuration: {str(e)}'})
        else:
            return JsonResponse({'success': False, 'error': 'Config file path not found'})

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@require_http_methods(["POST"])
def delete_spider(request, source_key):
    """Delete a spider and its files"""
    try:
        spider = get_object_or_404(Spider, source_key=source_key)

        # Delete files from filesystem
        spider_folder = os.path.join(settings.SPIDERS_DIR, spider.source_key)
        if os.path.exists(spider_folder):
            import shutil
            shutil.rmtree(spider_folder)

        # Delete from database
        spider.delete()

        messages.success(request, f'Spider {source_key} deleted successfully')
        return redirect('spiders:index')

    except Exception as e:
        messages.error(request, f'Error deleting spider: {str(e)}')
        return redirect('spiders:index')


def get_spider_code(request, source_key):
    """Get spider code for editing"""
    try:
        spider = get_object_or_404(Spider, source_key=source_key)

        spider_code = ""
        if spider.spider_file_path:
            spider_file_full_path = os.path.join(settings.SPIDERS_DIR, spider.spider_file_path)
            if os.path.exists(spider_file_full_path):
                with open(spider_file_full_path, 'r', encoding='utf-8') as f:
                    spider_code = f.read()

        return JsonResponse({'success': True, 'spider_code': spider_code})

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


def get_spider_config(request, source_key):
    """Get spider configuration for editing"""
    try:
        spider = get_object_or_404(Spider, source_key=source_key)

        config_json = {}
        if spider.config_file_path:
            config_file_full_path = os.path.join(settings.SPIDERS_DIR, spider.config_file_path)
            if os.path.exists(config_file_full_path):
                with open(config_file_full_path, 'r', encoding='utf-8') as f:
                    config_json = json.load(f)

        return JsonResponse({'success': True, 'config_json': config_json})

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})
