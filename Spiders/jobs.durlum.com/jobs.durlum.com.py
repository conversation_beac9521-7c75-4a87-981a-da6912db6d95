from jobsitescraper.log_manager import CustomLogger
import scrapy, re
import traceback, sys
from jobsitescraper.utils import env
from scrapy.exceptions import CloseSpider
from bs4 import BeautifulSoup
from datetime import datetime, timezone

class JobsDurlumComSpider(scrapy.Spider):
    name = 'jobs.durlum.com'
    close_down = False
    config = {}
    i = 0
    page_num = 1
    total = 0
    count = -1
    isLive = env('PRODUCTION')

    def __init__(self, _config=None, **kwargs):
        super().__init__(**kwargs)
        if self.isLive == 'True':
            self.config = _config
        else:
            self.config = self.get_config()

    def get_config(self):
        return {
            "SourceKey": "jobs.durlum.com",
            "BaseUrl": "https://jobs.durlum.com",
            "StartUrl": "https://jobs.durlum.com/de",
            "SourceCountry": "ch",
            "LangCode": "de",
            "Upload": True,
            "IsActive": True,
            "Custom": True,
            "MaxPagesToCrawl": 10,
            "MaxJobsToCrawl": 500,
            "RecentJobs": True,
            "DeleteAllJobsOnStart": True
        }

    def start_requests(self):
        try:
            CustomLogger.LogEvent(self.config['SourceKey'], "Spider started")
            if self.config.get('RecentJobs'):
                yield scrapy.Request(
                    url=self.config['StartUrl'],
                    callback=self.parse_recent,
                    errback=self.handle_error,
                    meta={'dont_redirect': True}
                )
        except Exception as e:
            CustomLogger.LogEvent(self.config['SourceKey'], str(e))
            CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())

    def handle_error(self, failure):
        CustomLogger.LogEvent(self.config['SourceKey'], str(failure))
        CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())

    def parse_recent(self, response):
        try:
            website_text = response.body.decode('utf-8')
            soup = BeautifulSoup(website_text.replace('<', ' <'), 'html.parser')
            for a in soup.find_all('a', href=True):
                link = a['href']
                if not link.startswith('http'):
                    link = response.urljoin(link)
                yield scrapy.Request(
                    url=link,
                    callback=self.parse_job,
                    errback=self.handle_error,
                    meta={'dont_redirect': True}
                )
            next_page = soup.find('a', string=re.compile(r'next', re.I))
            if next_page and self.page_num < self.config['MaxPagesToCrawl']:
                self.page_num += 1
                next_url = response.urljoin(next_page['href'])
                yield scrapy.Request(
                    url=next_url,
                    callback=self.parse_recent,
                    errback=self.handle_error,
                    meta={'dont_redirect': True}
                )
        except Exception as e:
            CustomLogger.LogEvent(self.config['SourceKey'], str(e))
            CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())

    def parse_job(self, response):
        try:
            if self.close_down:
                raise CloseSpider('took down by analyzer')
            website_text = response.body.decode('utf-8')
            soup = BeautifulSoup(website_text.replace('<', ' <'), 'html.parser')
            title_tag = soup.find('h1')
            jobTitle = title_tag.get_text(strip=True) if title_tag else ''
            location_tag = soup.find(string=re.compile('Standort', re.I))
            if location_tag:
                loc_parent = location_tag.find_parent()
                jobLocation = loc_parent.find_next('span')
                jobLocation = jobLocation.get_text(strip=True) if jobLocation else 'Switzerland'
            else:
                jobLocation = 'Switzerland'
            logo_tag = soup.find('img', alt=re.compile('Karriere', re.I))
            CompanyLogoFileURL = logo_tag['src'] if logo_tag and logo_tag.get('src') else 'https://jobs.durlum.com/default_logo.png'
            company_tag = soup.find('h3', class_='panel-title')
            CompanyName = company_tag.get_text(strip=True) if company_tag else ''
            # Description cleaning
            for tag in soup.find_all(['a','img','svg','script','style','footer','textarea','button','path','head']):
                tag.decompose()
            content_blocks = soup.find_all('div', class_='panel-body')
            raw_html = ''.join(str(block) for block in content_blocks)
            clean_text = ' '.join(block.get_text(separator=' ', strip=True) for block in content_blocks)
            cleanContent = re.sub(r'\s+', ' ', clean_text).strip()
            rawContent = raw_html
            PostedDate = datetime.now(timezone.utc).astimezone().isoformat()
            CrawlTimestamp = PostedDate
            SourceURL = response.url
            SourceCountry = self.config['SourceCountry']
            SourceKey = self.config['SourceKey']
            SourceLangCode = self.config['LangCode']
            SourceUID = response.url
            emails = re.findall(r'\S+@\S+', cleanContent)
            JobContactEmails = ', '.join(emails)
            phones = re.findall(r'[\+\(]?[1-9][0-9 \-\(\)]{8,}[0-9]', cleanContent.replace('\u00a0', ' '))
            JobContactPhone = ', '.join(phones)
            ad = {
                "JobTitle": jobTitle,
                "JobLocation": jobLocation,
                "CompanyLogoFileURL": CompanyLogoFileURL,
                "CompanyName": CompanyName,
                "SourceURL": SourceURL,
                "SourceCountry": SourceCountry,
                "SourceKey": SourceKey,
                "SourceLangCode": SourceLangCode,
                "CrawlTimestamp": CrawlTimestamp,
                "SourceUID": SourceUID,
                "CleanContent": cleanContent,
                "RawContent": rawContent,
                "PostedDate": PostedDate,
                "JobContactEmails": JobContactEmails,
                "JobContactPhone": JobContactPhone
            }
            self.count += 1
            if self.count >= self.config['MaxJobsToCrawl']:
                self.close_down = True
                raise CloseSpider('max jobs reached')
            yield ad
        except Exception as e:
            CustomLogger.LogEvent(self.config['SourceKey'], str(e))
            CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())

    def close(self, reason):
        try:
            CustomLogger.LogEvent(self.config['SourceKey'], f'Crawler Stopped, Total Jobs: {self.count}')
        except Exception:
            pass