from django.db import models
from django.utils import timezone
import json


class Spider(models.Model):
    """Model to represent a spider and its configuration"""

    # Basic spider information
    source_key = models.CharField(max_length=100, unique=True, help_text="Unique identifier for the spider")
    spider_class = models.CharField(max_length=100, help_text="Python class name of the spider")
    spider_filename = models.CharField(max_length=100, help_text="Filename for the spider")

    # URLs and configuration
    base_url = models.URLField(help_text="Base URL of the website")
    start_url = models.URLField(help_text="Starting URL for scraping")

    # Metadata
    company_name = models.CharField(max_length=200, blank=True, help_text="Company name extracted from the website")
    source_country = models.CharField(max_length=10, default='ch', help_text="Source country code")
    lang_code = models.CharField(max_length=10, default='de', help_text="Language code")

    # Configuration flags
    upload = models.<PERSON><PERSON>an<PERSON>ield(default=True)
    is_active = models.BooleanField(default=True)
    custom = models.BooleanField(default=True)
    has_error = models.BooleanField(default=False)
    depth = models.IntegerField(default=1)

    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    # File paths (relative to SPIDERS_DIR)
    spider_file_path = models.CharField(max_length=500, blank=True)
    config_file_path = models.CharField(max_length=500, blank=True)

    # Job statistics
    total_jobs_scraped = models.IntegerField(default=0, help_text="Total number of jobs scraped by this spider")

    class Meta:
        ordering = ['-updated_at']
        verbose_name = "Spider"
        verbose_name_plural = "Spiders"

    def __str__(self):
        return f"{self.source_key} - {self.company_name}"

    def get_config_dict(self):
        """Return the configuration as a dictionary"""
        return {
            "SourceKey": self.source_key,
            "BaseUrl": self.base_url,
            "StartUrl": self.start_url,
            "SourceCountry": self.source_country,
            "LangCode": self.lang_code,
            "CompanyName": self.company_name,
            "SpiderClass": self.spider_class,
            "SpiderFileName": self.spider_filename,
            "Upload": self.upload,
            "IsActive": self.is_active,
            "Custom": self.custom,
            "HasError": self.has_error,
            "Depth": self.depth
        }


class SpiderGenerationLog(models.Model):
    """Model to log spider generation attempts"""

    spider = models.ForeignKey(Spider, on_delete=models.CASCADE, related_name='generation_logs')
    start_url = models.URLField()
    success = models.BooleanField(default=False)
    error_message = models.TextField(blank=True)
    generation_time = models.DurationField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Spider Generation Log"
        verbose_name_plural = "Spider Generation Logs"

    def __str__(self):
        status = "Success" if self.success else "Failed"
        return f"{self.spider.source_key} - {status} - {self.created_at}"
