from django.urls import path
from . import views

app_name = 'spiders'

urlpatterns = [
    path('', views.index, name='index'),
    path('generate/', views.generate_spider, name='generate'),
    path('<str:source_key>/', views.spider_detail, name='detail'),
    path('<str:source_key>/code/', views.get_spider_code, name='get_code'),
    path('<str:source_key>/config/', views.get_spider_config, name='get_config'),
    path('<str:source_key>/save-code/', views.save_spider_code, name='save_code'),
    path('<str:source_key>/save-config/', views.save_spider_config, name='save_config'),
    path('<str:source_key>/delete/', views.delete_spider, name='delete'),
]
