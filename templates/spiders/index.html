{% extends 'base.html' %}

{% block title %}Spider Dashboard - Spider Manager{% endblock %}

{% block content %}
<div x-data="spiderDashboard()" x-cloak>
    <!-- Header Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Spider Dashboard</h1>
                <p class="mt-2 text-gray-600">Manage and generate web scraping spiders</p>
            </div>
            <div class="mt-4 sm:mt-0">
                <button @click="showGenerateModal = true" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2">
                    <i class="fas fa-plus"></i>
                    <span>Generate Spider</span>
                </button>
            </div>
        </div>
        
        <!-- Search Bar -->
        <div class="mt-8">
            <form method="get" class="flex gap-3">
                <div class="flex-1 relative">
                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input
                        type="text"
                        name="search"
                        value="{{ search_query }}"
                        placeholder="Search spiders by SourceKey..."
                        class="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm transition-all duration-200"
                    >
                </div>
                <button
                    type="submit"
                    class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-sm"
                >
                    <i class="fas fa-search mr-2"></i>
                    Search
                </button>
                {% if search_query %}
                    <a
                        href="{% url 'spiders:index' %}"
                        class="bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-sm"
                    >
                        <i class="fas fa-times mr-2"></i>
                        Clear
                    </a>
                {% endif %}
            </form>
        </div>

        <!-- Stats -->
        <div class="mt-6 grid grid-cols-1 gap-4">
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100 shadow-sm">
                <div class="flex items-center justify-center">
                    <i class="fas fa-spider text-blue-600 text-3xl mr-4"></i>
                    <div class="text-center">
                        <p class="text-sm text-blue-600 font-medium uppercase tracking-wide">Total Spiders</p>
                        <p class="text-4xl font-bold text-blue-900 mt-1">{{ total_spiders }}</p>
                        {% if search_query %}
                            <p class="text-xs text-blue-500 mt-1">{{ filtered_count }} found</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Spider Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 2xl:grid-cols-8 gap-4">
        {% for spider in page_obj %}
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg hover:border-blue-200 transition-all duration-300 transform hover:-translate-y-1">
            <div class="p-5">
                <!-- Spider Header -->
                <div class="mb-4">
                    <div class="flex items-center justify-between mb-2">
                        <i class="fas fa-spider text-blue-500 text-lg"></i>
                        {% if spider.is_active %}
                            <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                        {% else %}
                            <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                        {% endif %}
                    </div>
                    <h3 class="text-sm font-bold text-gray-900 truncate mb-1" title="{{ spider.source_key }}">{{ spider.source_key }}</h3>
                </div>

                <!-- Total Jobs -->
                <div class="mb-4">
                    <div class="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-3 text-center">
                        <p class="text-xs text-gray-500 uppercase tracking-wide font-medium">Total Jobs</p>
                        <p class="text-xl font-bold text-gray-900 mt-1">{{ spider.total_jobs_scraped|default:"0" }}</p>
                    </div>
                </div>

                <!-- Actions -->
                <div class="flex">
                    <a href="{% url 'spiders:detail' spider.source_key %}" class="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white text-center py-2.5 px-3 rounded-lg text-xs font-medium transition-all duration-200 transform hover:scale-105">
                        <i class="fas fa-eye mr-1"></i>
                        View
                    </a>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-span-full">
            <div class="text-center py-16">
                <div class="bg-gradient-to-br from-gray-100 to-gray-200 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-spider text-gray-400 text-4xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">
                    {% if search_query %}No spiders match your search{% else %}No spiders found{% endif %}
                </h3>
                <p class="text-gray-500 mb-8 max-w-md mx-auto">
                    {% if search_query %}
                        Try adjusting your search terms or <a href="{% url 'spiders:index' %}" class="text-blue-600 hover:text-blue-700 font-medium">view all spiders</a>
                    {% else %}
                        Get started by generating your first spider from a job listing URL
                    {% endif %}
                </p>
                {% if not search_query %}
                    <button @click="showGenerateModal = true" class="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-3 rounded-xl font-medium transition-all duration-200 transform hover:scale-105 shadow-lg">
                        <i class="fas fa-plus mr-2"></i>
                        Generate Your First Spider
                    </button>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="mt-12 flex justify-center">
        <nav class="flex items-center space-x-1">
            {% if page_obj.has_previous %}
                <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" class="px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <i class="fas fa-chevron-left"></i>
                </a>
            {% endif %}

            {% for num in page_obj.paginator.page_range %}
                {% if page_obj.number == num %}
                    <span class="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-blue-700 border border-blue-600 rounded-lg shadow-sm">{{ num }}</span>
                {% else %}
                    <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}" class="px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">{{ num }}</a>
                {% endif %}
            {% endfor %}

            {% if page_obj.has_next %}
                <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" class="px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <i class="fas fa-chevron-right"></i>
                </a>
            {% endif %}
        </nav>
    </div>
    {% endif %}

    <!-- Generate Spider Modal -->
    <div x-show="showGenerateModal" x-cloak class="fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div @click.away="showGenerateModal = false" class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Generate New Spider</h3>
                    <button @click="showGenerateModal = false" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form @submit.prevent="generateSpider()">
                    <div class="mb-4">
                        <label for="startUrl" class="block text-sm font-medium text-gray-700 mb-2">Job Listing URL</label>
                        <input 
                            type="url" 
                            id="startUrl" 
                            x-model="startUrl" 
                            required 
                            placeholder="https://example.com/jobs"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                        <p class="mt-1 text-xs text-gray-500">Enter the URL of the job listings page</p>
                    </div>
                    
                    <div class="flex space-x-3">
                        <button 
                            type="button" 
                            @click="showGenerateModal = false" 
                            class="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
                        >
                            Cancel
                        </button>
                        <button 
                            type="submit" 
                            :disabled="generating"
                            class="flex-1 px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <span x-show="!generating">Generate</span>
                            <span x-show="generating" class="flex items-center justify-center">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                Generating...
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function spiderDashboard() {
    return {
        showGenerateModal: false,
        generating: false,
        startUrl: '',
        
        async generateSpider() {
            if (!this.startUrl) return;
            
            this.generating = true;
            showLoading();
            
            try {
                const response = await fetch('{% url "spiders:generate" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrftoken
                    },
                    body: JSON.stringify({
                        start_url: this.startUrl
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showToast(`Spider ${data.action} successfully! Found ${data.job_links_found} job links.`, 'success');
                    this.showGenerateModal = false;
                    this.startUrl = '';
                    // Reload page to show new spider
                    setTimeout(() => window.location.reload(), 1500);
                } else {
                    showToast(`Error: ${data.error}`, 'error');
                }
            } catch (error) {
                showToast(`Error: ${error.message}`, 'error');
            } finally {
                this.generating = false;
                hideLoading();
            }
        }
    }
}
</script>
{% endblock %}
