{% extends 'base.html' %}

{% block title %}{{ spider.company_name|default:spider.source_key }} - Spider Manager{% endblock %}

{% block content %}
<div x-data="spiderDetail()" x-cloak>
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div class="flex items-center space-x-4">
                <a href="{% url 'spiders:index' %}" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-arrow-left text-xl"></i>
                </a>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">{{ spider.company_name|default:spider.source_key }}</h1>
                    <p class="text-gray-600">{{ spider.source_key }}</p>
                </div>
            </div>
            <div class="mt-4 sm:mt-0 flex items-center space-x-3">
                {% if spider.is_active %}
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        <i class="fas fa-circle text-green-400 mr-2" style="font-size: 8px;"></i>
                        Active
                    </span>
                {% else %}
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                        <i class="fas fa-circle text-gray-400 mr-2" style="font-size: 8px;"></i>
                        Inactive
                    </span>
                {% endif %}
                <button @click="confirmDelete = true" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200">
                    <i class="fas fa-trash mr-1"></i>
                    Delete
                </button>
            </div>
        </div>
        
        <!-- Spider Info -->
        <div class="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-sm text-gray-600 font-medium">Base URL</p>
                <p class="text-sm text-gray-900 truncate">{{ spider.base_url }}</p>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-sm text-gray-600 font-medium">Start URL</p>
                <p class="text-sm text-gray-900 truncate">{{ spider.start_url }}</p>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-sm text-gray-600 font-medium">Country/Language</p>
                <p class="text-sm text-gray-900">{{ spider.source_country|upper }} - {{ spider.lang_code|upper }}</p>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-sm text-gray-600 font-medium">Last Updated</p>
                <p class="text-sm text-gray-900">{{ spider.updated_at|date:"M d, Y H:i" }}</p>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="border-b border-gray-200">
            <nav class="flex space-x-8 px-6" aria-label="Tabs">
                <button 
                    @click="activeTab = 'code'" 
                    :class="activeTab === 'code' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                >
                    <i class="fas fa-code mr-2"></i>
                    Spider Code
                </button>
                <button 
                    @click="activeTab = 'config'" 
                    :class="activeTab === 'config' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                >
                    <i class="fas fa-cog mr-2"></i>
                    Configuration
                </button>
                <button 
                    @click="activeTab = 'logs'" 
                    :class="activeTab === 'logs' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                >
                    <i class="fas fa-history mr-2"></i>
                    Generation Logs
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="p-6">
            <!-- Spider Code Tab -->
            <div x-show="activeTab === 'code'" class="space-y-4">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">Spider Code</h3>
                    <div class="space-x-2">
                        <button 
                            @click="editMode.code = !editMode.code" 
                            :class="editMode.code ? 'bg-gray-600' : 'bg-blue-600'"
                            class="text-white px-4 py-2 rounded-lg font-medium hover:opacity-90 transition-opacity duration-200"
                        >
                            <i :class="editMode.code ? 'fas fa-eye' : 'fas fa-edit'" class="mr-1"></i>
                            <span x-text="editMode.code ? 'View' : 'Edit'"></span>
                        </button>
                        <button 
                            x-show="editMode.code" 
                            @click="saveCode()" 
                            :disabled="saving.code"
                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50"
                        >
                            <i class="fas fa-save mr-1"></i>
                            <span x-text="saving.code ? 'Saving...' : 'Save'"></span>
                        </button>
                    </div>
                </div>
                
                <div class="relative">
                    <textarea 
                        x-show="editMode.code"
                        x-model="spiderCode" 
                        class="w-full h-96 p-4 border border-gray-300 rounded-lg font-mono text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Spider code will appear here..."
                    ></textarea>
                    <pre x-show="!editMode.code" class="w-full h-96 p-4 border border-gray-300 rounded-lg font-mono text-sm bg-gray-50 overflow-auto"><code x-text="spiderCode"></code></pre>
                </div>
            </div>

            <!-- Configuration Tab -->
            <div x-show="activeTab === 'config'" class="space-y-4">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-900">Configuration</h3>
                    <div class="space-x-2">
                        <button 
                            @click="editMode.config = !editMode.config" 
                            :class="editMode.config ? 'bg-gray-600' : 'bg-blue-600'"
                            class="text-white px-4 py-2 rounded-lg font-medium hover:opacity-90 transition-opacity duration-200"
                        >
                            <i :class="editMode.config ? 'fas fa-eye' : 'fas fa-edit'" class="mr-1"></i>
                            <span x-text="editMode.config ? 'View' : 'Edit'"></span>
                        </button>
                        <button 
                            x-show="editMode.config" 
                            @click="saveConfig()" 
                            :disabled="saving.config"
                            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 disabled:opacity-50"
                        >
                            <i class="fas fa-save mr-1"></i>
                            <span x-text="saving.config ? 'Saving...' : 'Save'"></span>
                        </button>
                    </div>
                </div>
                
                <div class="relative">
                    <textarea 
                        x-show="editMode.config"
                        x-model="configJson" 
                        class="w-full h-96 p-4 border border-gray-300 rounded-lg font-mono text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Configuration JSON will appear here..."
                    ></textarea>
                    <pre x-show="!editMode.config" class="w-full h-96 p-4 border border-gray-300 rounded-lg font-mono text-sm bg-gray-50 overflow-auto"><code x-text="configJson"></code></pre>
                </div>
            </div>

            <!-- Generation Logs Tab -->
            <div x-show="activeTab === 'logs'" class="space-y-4">
                <h3 class="text-lg font-semibold text-gray-900">Generation Logs</h3>
                
                {% if recent_logs %}
                    <div class="space-y-3">
                        {% for log in recent_logs %}
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    {% if log.success %}
                                        <i class="fas fa-check-circle text-green-500"></i>
                                        <span class="text-green-700 font-medium">Success</span>
                                    {% else %}
                                        <i class="fas fa-times-circle text-red-500"></i>
                                        <span class="text-red-700 font-medium">Failed</span>
                                    {% endif %}
                                </div>
                                <span class="text-sm text-gray-500">{{ log.created_at|date:"M d, Y H:i" }}</span>
                            </div>
                            <div class="mt-2">
                                <p class="text-sm text-gray-600">URL: {{ log.start_url }}</p>
                                {% if log.generation_time %}
                                    <p class="text-sm text-gray-600">Duration: {{ log.generation_time }}</p>
                                {% endif %}
                                {% if log.error_message %}
                                    <p class="text-sm text-red-600 mt-1">Error: {{ log.error_message }}</p>
                                {% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-8">
                        <i class="fas fa-history text-gray-400 text-4xl mb-4"></i>
                        <p class="text-gray-500">No generation logs found</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div x-show="confirmDelete" x-cloak class="fixed inset-0 bg-gray-600 bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div @click.away="confirmDelete = false" class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <i class="fas fa-exclamation-triangle text-red-500 text-2xl mr-3"></i>
                    <h3 class="text-lg font-semibold text-gray-900">Confirm Delete</h3>
                </div>
                
                <p class="text-gray-600 mb-6">
                    Are you sure you want to delete this spider? This action cannot be undone and will remove all associated files.
                </p>
                
                <div class="flex space-x-3">
                    <button 
                        @click="confirmDelete = false" 
                        class="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"
                    >
                        Cancel
                    </button>
                    <form method="post" action="{% url 'spiders:delete' spider.source_key %}" class="flex-1">
                        {% csrf_token %}
                        <button 
                            type="submit" 
                            class="w-full px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700"
                        >
                            Delete Spider
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function spiderDetail() {
    return {
        activeTab: 'code',
        editMode: {
            code: false,
            config: false
        },
        saving: {
            code: false,
            config: false
        },
        confirmDelete: false,
        spiderCode: `{{ spider_code|escapejs }}`,
        configJson: `{{ config_json|escapejs }}`,
        
        async saveCode() {
            this.saving.code = true;
            
            try {
                const response = await fetch('{% url "spiders:save_code" spider.source_key %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrftoken
                    },
                    body: JSON.stringify({
                        spider_code: this.spiderCode
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showToast('Spider code saved successfully!', 'success');
                    this.editMode.code = false;
                } else {
                    showToast(`Error: ${data.error}`, 'error');
                }
            } catch (error) {
                showToast(`Error: ${error.message}`, 'error');
            } finally {
                this.saving.code = false;
            }
        },
        
        async saveConfig() {
            this.saving.config = true;
            
            try {
                // Validate JSON
                JSON.parse(this.configJson);
                
                const response = await fetch('{% url "spiders:save_config" spider.source_key %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrftoken
                    },
                    body: JSON.stringify({
                        config_json: JSON.parse(this.configJson)
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showToast('Configuration saved successfully!', 'success');
                    this.editMode.config = false;
                } else {
                    showToast(`Error: ${data.error}`, 'error');
                }
            } catch (error) {
                if (error instanceof SyntaxError) {
                    showToast('Invalid JSON format', 'error');
                } else {
                    showToast(`Error: ${error.message}`, 'error');
                }
            } finally {
                this.saving.config = false;
            }
        }
    }
}
</script>
{% endblock %}
