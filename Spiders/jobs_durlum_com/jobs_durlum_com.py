from jobsitescraper.log_manager import CustomLogger
import scrapy, re
import traceback, sys
from jobsitescraper.utils import env
from scrapy.exceptions import CloseSpider
from bs4 import BeautifulSoup
from datetime import datetime, timezone

class jobsDurlumComSpider(scrapy.Spider):
    name = 'jobs.durlum.com'
    close_down = False
    config = {}
    i = 0
    page_num = 1
    total = 0
    count = -1
    isLive = env('PRODUCTION')

    def __init__(self, _config=None, **kwargs):
        super().__init__(**kwargs)
        if self.isLive == 'True':
            self.config = _config
        else:
            self.config = self.get_config()

    def get_config(self):
        return {
            'SourceKey': 'jobs_durlum_com',
            'BaseUrl': 'https://jobs.durlum.com',
            'StartUrl': 'https://jobs.durlum.com/de',
            'SourceCountry': 'ch',
            'LangCode': 'de',
            'Upload': True,
            'IsActive': True,
            'Custom': True,
            'MaxPagesToCrawl': 10,
            'MaxJobsToCrawl': 500,
            'RecentJobs': True,
            'DeleteAllJobsOnStart': True
        }

    def start_requests(self):
        try:
            CustomLogger.LogEvent(self.config['SourceKey'], 'Crawler started')
            if self.config.get('RecentJobs'):
                yield scrapy.Request(
                    url=self.config['StartUrl'],
                    callback=self.parse_recent,
                    dont_filter=True
                )
        except Exception as e:
            CustomLogger.LogEvent(self.config['SourceKey'], str(e))
            CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())

    def parse_recent(self, response):
        try:
            website_text = response.body.decode('utf-8')
            soup = BeautifulSoup(website_text.replace('<', ' <'), 'html.parser')
            links = []
            for a in soup.find_all('a', href=True):
                href = a['href']
                if '?id=' in href:
                    if href.startswith('http'):
                        links.append(href)
                    else:
                        links.append(self.config['BaseUrl'].rstrip('/') + '/' + href.lstrip('/'))
            for link in links:
                yield scrapy.Request(url=link, callback=self.parse_job, dont_filter=True)
        except Exception as e:
            CustomLogger.LogEvent(self.config['SourceKey'], str(e))
            CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())

    def parse_job(self, response):
        try:
            if self.close_down:
                raise CloseSpider('took down by analyzer')
            website_text = response.body.decode('utf-8')
            soup = BeautifulSoup(website_text.replace('<', ' <'), 'html.parser')
            title_tag = soup.find('h1')
            jobTitle = title_tag.get_text(strip=True) if title_tag else ''
            location = ''
            loc_header = soup.find('h6', string=lambda s: s and 'Standort' in s)
            if loc_header:
                loc_span = loc_header.find_next('span')
                if loc_span:
                    location = loc_span.get_text(strip=True)
            jobLocation = re.sub(r'[\xa0]', ' ', location) if location else 'Switzerland'
            logo_tag = soup.find('img', alt=re.compile('Karriere', re.I))
            if logo_tag and logo_tag.get('src'):
                CompanyLogoFileURL = logo_tag['src']
                if CompanyLogoFileURL.startswith('//'):
                    CompanyLogoFileURL = 'https:' + CompanyLogoFileURL
                elif not CompanyLogoFileURL.startswith('http'):
                    CompanyLogoFileURL = self.config['BaseUrl'].rstrip('/') + '/' + CompanyLogoFileURL.lstrip('/')
            else:
                CompanyLogoFileURL = self.config['BaseUrl'].rstrip('/') + '/default_logo.png'
            company_tag = soup.find('h3', class_='panel-title')
            CompanyName = company_tag.get_text(strip=True) if company_tag else ''
            for tag in soup(['a','img','svg','script','style','footer','textarea','button','path','head']):
                tag.decompose()
            rawContent = str(soup)
            clean_text = soup.get_text(separator=' ', strip=True)
            cleanContent = re.sub(r'\s+', ' ', clean_text)
            JobContactEmails = ', '.join(re.findall(r'\S+@\S+', cleanContent.strip('\n')))
            JobContactPhone = ', '.join(re.findall(r'[\+\(]?[1-9][0-9 \-\(\)]{8,}[0-9]', cleanContent.strip('\n').replace('\u00a0', ' ')))
            CrawlTimestamp = datetime.now(timezone.utc).astimezone().isoformat()
            ad = {
                'JobTitle': jobTitle,
                'JobLocation': jobLocation,
                'CompanyLogoFileURL': CompanyLogoFileURL,
                'CompanyName': CompanyName,
                'SourceURL': response.url,
                'SourceCountry': self.config['SourceCountry'],
                'SourceKey': self.config['SourceKey'],
                'SourceLangCode': self.config['LangCode'],
                'CrawlTimestamp': CrawlTimestamp,
                'SourceUID': response.url,
                'CleanContent': cleanContent,
                'RawContent': rawContent,
                'PostedDate': CrawlTimestamp,
                'JobContactEmails': JobContactEmails,
                'JobContactPhone': JobContactPhone
            }
            self.count += 1
            if self.count >= self.config['MaxJobsToCrawl']:
                self.close_down = True
                raise CloseSpider('max jobs reached')
            yield ad
        except Exception as e:
            CustomLogger.LogEvent(self.config['SourceKey'], str(e))
            CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())

    def close(self, reason):
        try:
            CustomLogger.LogEvent(self.config['SourceKey'], f'Crawler Stopped, Total Jobs: {self.count}')
        except Exception:
            pass