from jobsitescraper.log_manager import CustomLogger
import scrapy, re
import traceback, sys
from jobsitescraper.utils import env
from scrapy.exceptions import CloseSpider
from bs4 import BeautifulSoup
from datetime import datetime, timezone

class mooserpartnerChSpider(scrapy.Spider):
    name = 'mooserpartner.ch'
    close_down = False
    config = {}
    i = 0
    page_num = 1
    total = 0
    count = -1
    isLive = env('PRODUCTION')

    def __init__(self, _config=None, **kwargs):
        super().__init__(**kwargs)
        if self.isLive == 'True':
            self.config = _config
        else:
            self.config = self.get_config()

    def get_config(self):
        return {
            "SourceKey": "mooserpartner_ch",
            "BaseUrl": "https://www.mooserpartner.ch",
            "StartUrl": "https://www.mooserpartner.ch/fuer-privatpersonen/job-liste.htm",
            "SourceCountry": "ch",
            "LangCode": "de",
            "Upload": True,
            "IsActive": True,
            "Custom": True,
            "MaxPagesToCrawl": 10,
            "MaxJobsToCrawl": 500,
            "RecentJobs": True,
            "DeleteAllJobsOnStart": True
        }

    def start_requests(self):
        try:
            CustomLogger.LogEvent(self.config['SourceKey'], "Spider started")
            if self.config.get('RecentJobs'):
                yield scrapy.Request(url=self.config['StartUrl'], callback=self.parse_recent, dont_filter=True)
        except Exception as e:
            CustomLogger.LogEvent(self.config['SourceKey'], str(e))
            CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())

    def parse_recent(self, response):
        try:
            website_text = response.body.decode('utf-8')
            soup = BeautifulSoup(website_text.replace('<', ' <'), 'html.parser')
            for a in soup.find_all('a', href=True):
                href = a['href']
                if '/fuer-privatpersonen/job-liste/' in href and not href.lower().endswith('.pdf'):
                    link = href if href.startswith('http') else self.config['BaseUrl'] + href
                    yield scrapy.Request(url=link, callback=self.parse_job, dont_filter=True)
            next_page = soup.find('a', string=re.compile(r'(?i)weiter|next'))
            if next_page and self.page_num < self.config['MaxPagesToCrawl']:
                next_href = next_page['href']
                next_url = next_href if next_href.startswith('http') else self.config['BaseUrl'] + next_href
                self.page_num += 1
                yield scrapy.Request(url=next_url, callback=self.parse_recent, dont_filter=True)
        except Exception as e:
            CustomLogger.LogEvent(self.config['SourceKey'], str(e))
            CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())

    def parse_job(self, response):
        if self.close_down:
            raise CloseSpider('took down by analyzer')
        try:
            soup = BeautifulSoup(response.body.decode('utf-8'), 'html.parser')
            title_tag = soup.find('h1')
            jobTitle = title_tag.get_text(strip=True) if title_tag else ''
            location_tag = soup.find('span', class_=re.compile(r'icon-location'))
            jobLocation = ''
            if location_tag and location_tag.parent:
                jobLocation = location_tag.parent.get_text(strip=True)
            jobLocation = re.sub(r'\xa0', ' ', jobLocation).strip()
            if not jobLocation:
                jobLocation = 'Switzerland'
            logo_tag = soup.find('img', class_=re.compile(r'logo'))
            CompanyLogoFileURL = logo_tag['src'] if logo_tag and logo_tag.get('src') else self.config['BaseUrl'] + '/includes/images/default_logo.png'
            if not CompanyLogoFileURL.startswith('http'):
                CompanyLogoFileURL = self.config['BaseUrl'] + CompanyLogoFileURL
            company_name_tag = soup.find('strong')
            CompanyName = company_name_tag.get_text(strip=True) if company_name_tag else 'Mooser & Partner AG'
            raw_html = response.body.decode('utf-8')
            for tag in soup(['a','img','svg','script','style','footer','textarea','button','path','head']):
                tag.decompose()
            cleanContent = re.sub(r'\s+', ' ', soup.get_text()).strip()
            posted_date = datetime.now(timezone.utc).astimezone().isoformat()
            ad = {
                "JobTitle": jobTitle,
                "JobLocation": jobLocation,
                "CompanyLogoFileURL": CompanyLogoFileURL,
                "CompanyName": CompanyName,
                "SourceURL": response.url,
                "SourceCountry": self.config['SourceCountry'],
                "SourceKey": self.config['SourceKey'],
                "SourceLangCode": self.config['LangCode'],
                "CrawlTimestamp": posted_date,
                "SourceUID": response.url,
                "CleanContent": cleanContent,
                "RawContent": raw_html,
                "PostedDate": posted_date,
                "JobContactEmails": ','.join(re.findall(r'\S+@\S+', cleanContent)),
                "JobContactPhone": ','.join(re.findall(r'[\+\(]?[1-9][0-9 \-\(\)]{8,}[0-9]', cleanContent.replace('\u00a0', ' ')))
            }
            self.count += 1
            if self.count >= self.config['MaxJobsToCrawl']:
                self.close_down = True
                raise CloseSpider('max jobs reached')
            yield ad
        except Exception as e:
            CustomLogger.LogEvent(self.config['SourceKey'], str(e))
            CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())

    def close(self, reason):
        try:
            CustomLogger.LogEvent(self.config['SourceKey'], f'Crawler Stopped, Total Jobs: {self.count}')
        except Exception as e:
            CustomLogger.LogEvent(self.config['SourceKey'], str(e))
            CustomLogger.LogEvent(self.config['SourceKey'], traceback.format_exc())